services:
  api:
    image: ${IMAGE:-deadlock-api-rust:latest}
    build: .
    env_file: .env.testing
    environment:
      REDIS_URL: redis://default:sdfdsmplvmdfs@redis:6379/0
      CLICKHOUSE_HOST: clickhouse
      POSTGRES_HOST: postgres
      S3_ENDPOINT: http://minio:9000
      S3_CACHE_ENDPOINT: http://minio:9000
    ports:
      - "3000:3000"
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:3000/v1/info/health" ]
      interval: 10s
      timeout: 10s
      retries: 5
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      clickhouse:
        condition: service_healthy
      minio:
        condition: service_healthy
  postgres:
    image: postgres:17
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: root
    healthcheck:
      test: [ "CMD", "pg_isready", "-U", "postgres" ]
      interval: 10s
      timeout: 10s
      retries: 5
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    command: redis-server --requirepass sdfdsmplvmdfs
    healthcheck:
      test: [ "CMD", "redis-cli", "-a", "sdfdsmplvmdfs", "ping" ]
      interval: 10s
      timeout: 10s
      retries: 5
  clickhouse:
    image: clickhouse/clickhouse-server:25.3
    ports:
      - "8123:8123"
      - "9000:9000"
    env_file: .env.testing
    environment:
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ijojdmkasd
      CLICKHOUSE_DB: default
    healthcheck:
      test: [ "CMD-SHELL", "wget --no-verbose --tries=1 --spider ****************************************/?query=SELECT%201 || exit 1" ]
      interval: 10s
      timeout: 10s
      retries: 5
  minio:
    image: minio/minio
    ports:
      - "9001:9000"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    command: server /data
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:9000/minio/health/live" ]
      interval: 10s
      timeout: 10s
      retries: 5
