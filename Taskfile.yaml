version: '3'

env:
  TESTING_DOCKER_COMPOSE: docker-compose.testing.yaml
  TESTING_ENV_FILE: .env.testing

tasks:
  format:
    desc: "Formats the code using cargo fmt."
    cmds:
      - cargo fmt --all

  format:check:
    desc: "Checks if the code is formatted using cargo fmt."
    cmds:
      - cargo fmt --all -- --check

  lint:
    desc: "Runs cargo clippy for linting."
    deps:
      - task: format:check
    cmds:
      - cargo clippy --all-targets --all-features -- -D warnings

  teardown-test-env:
    desc: "Stops and removes the testing Docker containers."
    cmds:
      - docker compose -f $TESTING_DOCKER_COMPOSE down

  start-test-env-api-deps:
    desc: "Starts the API dependencies for testing."
    env:
      NO_BUILD: 0
      IMAGE: deadlock-api-rust:latest
    cmds:
      - "if [[ $NO_BUILD = 1 ]]; then docker compose -f $TESTING_DOCKER_COMPOSE up -d --wait --remove-orphans clickhouse minio postgres redis; fi"
      - "if [[ $NO_BUILD = 0 ]]; then docker compose -f $TESTING_DOCKER_COMPOSE up -d --wait --remove-orphans --build clickhouse minio postgres redis; fi"

  start-test-env:
    desc: "Starts the testing Docker environment."
    env:
      NO_BUILD: 0
      IMAGE: deadlock-api-rust:latest
    cmds:
      - task: start-test-env-api-deps
      - "if [[ $NO_BUILD = 1 ]]; then docker compose -f $TESTING_DOCKER_COMPOSE up -d --wait --remove-orphans; fi"
      - "if [[ $NO_BUILD = 0 ]]; then docker compose -f $TESTING_DOCKER_COMPOSE up -d --wait --remove-orphans --build; fi"

  import-test-data-postgres:
    desc: "Imports test data into PostgreSQL."
    dotenv: [ '{{.TESTING_ENV_FILE}}' ]
    preconditions:
      - '[[ $(docker compose -f $TESTING_DOCKER_COMPOSE ps --format json postgres | jq ".Health") != "healthy" ]]'
    vars:
      FILE:
        sh: find tests/data/postgres/ -type f -name '*.sql'
    cmds:
      - for: { var: FILE }
        cmd: docker compose -f $TESTING_DOCKER_COMPOSE exec -T postgres psql -U $POSTGRES_USERNAME -d $POSTGRES_DBNAME < {{.ITEM}}

  import-test-data-clickhouse:
    desc: "Imports test data into ClickHouse."
    dotenv: [ '{{.TESTING_ENV_FILE}}' ]
    preconditions:
      - '[[ $(docker compose -f $TESTING_DOCKER_COMPOSE ps --format json clickhouse | jq ".Health") != "healthy" ]]'
    vars:
      FILE:
        sh: find tests/data/clickhouse/ -type f -name '*.sql'
    cmds:
      - for: { var: FILE }
        cmd: docker compose -f $TESTING_DOCKER_COMPOSE exec -T clickhouse clickhouse-client -u $CLICKHOUSE_USERNAME --password $CLICKHOUSE_PASSWORD < {{.ITEM}}

  import-test-data:
    desc: "Sets up the testing Docker environment."
    dotenv: [ '{{.TESTING_ENV_FILE}}' ]
    deps:
      - task: import-test-data-postgres
      - task: import-test-data-clickhouse

  setup-test-env:
    desc: "Sets up the testing Docker environment."
    dotenv: [ '{{.TESTING_ENV_FILE}}' ]
    cmds:
      - task: start-test-env
      - task: import-test-data

  test:
    desc: "Runs the cargo tests after setting up the test environment."
    dotenv: [ '{{.TESTING_ENV_FILE}}' ]
    deps:
      - task: setup-test-env
    cmds:
      - cargo nextest run
      - task: teardown-test-env
