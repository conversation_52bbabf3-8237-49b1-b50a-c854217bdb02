[package]
name = "deadlock-api-rust"
version = "0.1.0"
edition = "2024"
description = "API for Deadlock game data"
repository = "https://github.com/deadlock-api/deadlock-api-rust"
license = "MIT"

[profile.release]
lto = "fat"
codegen-units = 1
opt-level = 3

[dependencies]
reqwest = { version = "0.12.19", features = ["json"] }
tokio = { version = "1.45.1", features = ["macros", "rt-multi-thread"] }
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
redis = { version = "0.31.0", features = ["tokio-comp"], default-features = false }
envy = "0.4.2"
clickhouse = "0.13.3"
sqlx = { version = "0.8.6", features = ["macros", "postgres", "runtime-tokio", "uuid"] }
uuid = { version = "1.17.0", features = ["v4"] }
cached = { version = "0.55.1", features = ["async", "serde", "tokio"] }
axum = { version = "0.8.4", features = ["http2"] }
utoipa-axum = "0.2.0"
utoipa = { version = "5.3.1", features = ["axum_extras", "chrono"] }
serde = { version = "1.0.219", features = ["derive"] }
chrono = { version = "0.4.41", features = ["serde"] }
serde_json = "1.0.140"
derive_more = { version = "2.0.1", features = ["constructor", "display", "from", "is_variant"] }
object_store = { version = "0.12.1", features = ["aws"], default-features = false }
tower = "0.5.2"
tower-layer = "0.3.3"
tower-service = "0.3.3"
tower-http = { version = "0.6.5", features = ["compression-zstd", "compression-gzip", "cors", "decompression-zstd", "decompression-gzip", "normalize-path", "trace"] }
tracing = "0.1.41"
utoipa-scalar = { version = "0.3.0", features = ["axum"] }
serde-xml-rs = "0.8.1"
axum-prometheus = "0.8.0"
itertools = "0.14.0"
futures = "0.3.31"
snap = "1.1.1"
valveprotos = { git = "https://github.com/deadlock-api/valveprotos-rs.git", rev = "8618612dfc39e29b895c8cb726a98963ade5a557", features = ["gc-client", "serde"] }
prost = "0.13.5"
base64 = "0.22.1"
tryhard = "0.5.1"
async-compression = { version = "0.4.23", features = ["bzip2", "tokio"] }
strum = { version = "0.27.1", features = ["derive"] }
strum_macros = "0.27.1"
hmac = "0.12.1"
sha2 = "0.10.9"
hex = "0.4.3"
axum-extra = { version = "0.10.1", features = ["query"] }
metrics = "0.24.2"
haste = { git = "https://github.com/deadlock-api/haste.git", rev = "b1ca28ba63389f11a00f08d5b60df49b00f7c800" }
async-stream = "0.3.6"
bytes = { version = "1.10.1", features = ["serde"] }
thiserror = "2.0.12"

[dev-dependencies]
rstest = "0.25.0"
