DROP TABLE IF EXISTS items;

CREATE TABLE items
(
    id   UInt32,
    name String,
    tier UInt8
) engine = ReplacingMergeTree PRIMARY KEY id ORDER BY id;

INSERT INTO default.items (id, name, tier)
VALUES (7409189, 'Improved Spirit', 2),
       (26002154, 'Melee Charge', 2),
       (84321454, 'Quicksilver Reload', 2),
       (98582110, 'Backstabber', 2),
       (112198670, 'Spirit Shielding', 2),
       (334300056, 'Stamina Mastery', 3),
       (339443430, 'Frenzy', 4),
       (343572757, 'Spirit Burn', 4),
       (365620721, 'Glass Cannon', 4),
       (380806748, 'Compress Cooldown', 2),
       (381961617, 'Active Reload', 2),
       (393974127, 'Slowing Bullets', 2),
       (395867183, 'Mystic Shot', 2),
       (395944548, 'Torment Pulse', 3),
       (465043967, 'Spirit Strike', 1),
       (493591231, 'Lightning Scroll', 4),
       (499683006, 'Bullet Lifesteal', 2),
       (600033864, '<PERSON>est<PERSON> Leap', 3),
       (619484391, '<PERSON> Wave', 3),
       (630839635, 'Echo Shard', 4),
       (668299740, 'Rapid Rounds', 1),
       (673001892, 'Armor <PERSON>cing Rounds', 4),
       (677738769, 'Refresher', 4),
       (690458959, '<PERSON> Shot', 3),
       (709540378, 'Cultist Sacrifice', 3),
       (710436191, 'Capacitor', 4),
       (754480263, 'Mystic Expansion', 1),
       (787198704, 'Rapid Recharge', 3),
       (800008313, 'Crushing Fists', 4),
       (805079544, 'Weapon Shielding', 2),
       (811521119, 'Tesla Bullets', 3),
       (857669956, 'Guardian Ward', 2),
       (865846625, 'Leech', 4),
       (865958998, 'Veil Walker', 3),
       (869090587, 'Mystic Reverb', 4),
       (876563814, 'Spirit Lifesteal', 2),
       (968099481, 'Extra Spirit', 1),
       (989206714, 'Blood Tribute', 3),
       (1009965641, 'Monster Rounds', 1),
       (1047818222, 'Debuff Reducer', 2),
       (1055679805, 'Vampiric Burst', 4),
       (1102081447, 'Mystic Slow', 2),
       (1113837674, 'Silencer', 4),
       (1144549437, 'Spirit Shredder Bullets', 2),
       (1150006784, 'Arcane Surge', 2),
       (1152158042, 'Vortex Web', 4),
       (1193964439, 'Greater Expansion', 3),
       (1219329868, 'Spirit Sap', 2),
       (1235347618, 'Battle Vest', 2),
       (1250307611, 'Juggernaut', 4),
       (1252627263, 'Lifestrike', 3),
       (1254091416, 'Knockdown', 3),
       (1282141666, 'Siphon Bullets', 4),
       (1292979587, 'Surge of Power', 3),
       (1342610602, 'Close Quarters', 1),
       (1371725689, 'Phantom Strike', 4),
       (1378931225, 'Metal Skin', 3),
       (1396247347, 'Lucky Shot', 4),
       (1409190604, 'Fury Trance', 3),
       (1414025773, 'Counterspell', 3),
       (1414319208, 'Berserker', 3),
       (1427630806, 'Healing Tempo', 4),
       (1437614329, 'Melee Lifesteal', 1),
       (1439347412, 'Mystic Regeneration', 1),
       (1548066885, 'Extended Magazine', 1),
       (1644605047, 'Reactive Barrier', 2),
       (1662311306, 'Divine Barrier', 4),
       (1710079648, 'Healing Rite', 1),
       (1770441818, 'Weakening Headshot ', 2),
       (1797283378, 'Infuser', 4),
       (1798666702, 'Shadow Weave', 4),
       (1804594021, 'Rescue Beam', 3),
       (1813726886, 'Slowing Hex', 2),
       (1925087134, 'Suppressor', 2),
       (1932939246, 'Alchemical Fire', 3),
       (1955841979, 'Spellbreaker', 4),
       (1976391348, 'Cold Front', 2),
       (1998374645, 'Mystic Burst', 1),
       (2010028405, 'Headshot Booster', 1),
       (2037039379, 'Inhibitor', 4),
       (2059712766, 'Restorative Locket', 2),
       (2061878743, 'Disarming Hex', 3),
       (2064029594, 'Opening Rounds', 2),
       (2081037738, 'Mystic Vulnerability', 2),
       (2095565695, 'Point Blank', 3),
       (2108215830, 'Heroic Aura', 3),
       (2121044373, 'Tankbuster', 3),
       (2142980412, 'Focus Lens', 4),
       (2152872419, 'Sharpshooter', 3),
       (2163598980, 'Spirit Resilience', 3),
       (2221211450, 'Spellslinger', 4),
       (2226497419, 'Spiritual Overflow', 4),
       (2356412290, 'Titanic Magazine', 2),
       (2407033488, 'Intensifying Magazine', 2),
       (2407781327, 'Colossus', 4),
       (2417568017, 'Scourge', 4),
       (2447176615, 'Enduring Speed', 2),
       (2463960640, 'Escalating Resilience', 3),
       (2480592370, 'Ricochet', 4),
       (2481177645, 'Hunter\'s Aura', 3),
       (2519598785, 'Boundless Spirit', 4),
       (2533252781, 'Ethereal Shift', 4),
       (2566692615, 'Healing Booster', 2),
       (2603935618, 'Healbane', 2),
       (2617435668, 'Curse', 4),
       (2678489038, 'Hollow Point', 3),
       (2717651715, 'Superior Duration', 3),
       (2739107182, 'Burst Fire', 3),
       (2800629741, 'Magic Carpet', 4),
       (2820116164, 'Diviner\'s Kevlar', 4),
       (2829638276, 'Extra Regen', 1),
       (2922054143, 'Rusted Barrel', 1),
       (2947183272, 'Radiant Regeneration', 3),
       (2951612397, 'Duration Extender', 2),
       (2956256701, 'Healing Nova', 3),
       (2971868509, 'Bullet Resist Shredder', 2),
       (3005970438, 'Escalating Exposure', 4),
       (3028234315, 'Witchmail', 4),
       (3074274290, 'Trophy Collector', 3),
       (3077079169, 'High-Velocity Rounds', 1),
       (3140772621, 'Bullet Resilience', 3),
       (**********, 'Decay', 3),
       (**********, 'Spirit Snatch', 3),
       (**********, 'Superior Cooldown', 3),
       (**********, 'Warp Stone', 3),
       (**********, 'Long Range', 2),
       (**********, 'Unstoppable', 4),
       (**********, 'Return Fire', 2),
       (**********, 'Cheat Death', 4),
       (**********, 'Sprint Boots', 1),
       (**********, 'Fleetfoot', 2),
       (**********, 'Plated Armor', 4),
       (**********, 'Fortitude', 3),
       (**********, 'Extra Health', 1),
       (**********, 'Split Shot', 2),
       (**********, 'Toxic Bullets', 3),
       (**********, 'Debuff Remover', 3),
       (**********, 'Extra Charge', 1),
       (**********, 'Weighted Shots', 3),
       (**********, 'Arctic Blast', 4),
       (**********, 'Restorative Shot', 1),
       (**********, 'Crippling Headshot', 4),
       (**********, 'Mercurial Magnum', 4),
       (**********, 'Enchanter\'s Emblem', 2),
       (**********, 'Kinetic Dash', 2),
       (**********, 'Headhunter', 3),
       (**********, 'Spirit Rend', 3),
       (**********, 'Swift Striker', 2),
       (**********, 'Extra Stamina', 1),
       (**********, 'Rebuttal', 1);
