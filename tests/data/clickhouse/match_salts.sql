DROP TABLE IF EXISTS match_salts;

CREATE TABLE match_salts
(
    match_id      UInt64,
    cluster_id    Nullable(UInt32),
    metadata_salt Nullable(UInt32) default 0,
    replay_salt   Nullable(UInt32),
    created_at    DateTime         default now(),
    username      Nullable(String)
) engine = ReplacingMergeTree ORDER BY match_id
      SETTINGS index_granularity = 8192;

INSERT INTO match_salts (match_id, cluster_id, metadata_salt, replay_salt, created_at)
VALUES (34000002, 396, 2142061196, 157889347, '2025-03-17 13:36:18'),
       (34000003, 118, 1105612624, 2085935243, '2025-03-18 07:28:26'),
       (34000007, 346, 620440843, 706701169, '2025-03-17 06:45:44'),
       (34000009, 404, 1120000902, 151556795, '2025-03-17 09:28:16'),
       (34000010, 187, 1228940450, 2001363015, '2025-03-20 14:39:09'),
       (34000012, 404, 235849034, 295977627, '2025-03-17 13:29:26'),
       (34000013, 392, 179385290, 1519922168, '2025-03-17 09:28:15'),
       (34000015, 390, 2006557465, 1001986877, '2025-03-17 12:59:10'),
       (34000017, 125, 1028912781, 887918399, '2025-03-17 14:29:31'),
       (34000019, 125, 456802641, 67110091, '2025-03-17 09:28:14'),
       (34000021, 404, 761611669, 1768047738, '2025-03-18 07:27:35'),
       (34000024, 392, 1236587684, 626463917, '2025-03-20 14:38:37'),
       (34000027, 152, 716050045, 2028841138, '2025-03-17 09:28:14'),
       (34000029, 404, 1813293666, 184280332, '2025-03-20 14:37:05'),
       (34000030, 392, 688882969, 1504499426, '2025-03-18 07:27:34'),
       (34000031, 392, 1543010091, 1329540395, '2025-03-18 11:27:59'),
       (34000032, 389, 1932530529, 1734278919, '2025-03-17 09:28:14'),
       (34000034, 129, 958483977, 1219475373, '2025-03-17 20:25:25'),
       (34000038, 400, 873201776, 2083469037, '2025-03-17 06:51:26'),
       (34000040, 392, 1985989790, 1251793435, '2025-03-20 14:37:04'),
       (34000044, 404, 962776003, 745008598, '2025-03-17 09:28:13'),
       (34000045, 389, 1935561570, 475291705, '2025-03-17 08:14:29'),
       (34000047, 392, 751562366, 823721883, '2025-03-18 07:27:34'),
       (34000048, 392, 850660917, 974025258, '2025-03-17 20:29:46'),
       (34000049, 392, 671017297, 924841446, '2025-03-17 12:59:10'),
       (34000051, 400, 87320870, 949377223, '2025-03-18 11:53:34'),
       (34000052, 392, 462838221, 934398176, '2025-03-17 09:28:12'),
       (34000054, 172, 200384156, 331732999, '2025-03-18 19:11:17'),
       (34000055, 403, 1434204387, 1209749550, '2025-03-17 09:28:12'),
       (34000057, 186, 670241951, 436173834, '2025-03-17 09:28:11'),
       (34000062, 152, 1502673143, 1414871333, '2025-03-17 19:29:58'),
       (34000064, 125, 81987861, 1614908957, '2025-03-18 07:27:33'),
       (34000065, 404, 1715002780, 1167382971, '2025-03-17 17:00:09'),
       (34000066, 392, 182469831, 2067490995, '2025-03-17 10:31:58'),
       (34000067, 186, 1369892331, 281483538, '2025-03-17 20:25:24'),
       (34000068, 389, 1508950201, 1902944721, '2025-03-17 13:29:25'),
       (34000070, 404, 1400546375, 686018269, '2025-03-17 20:01:28'),
       (34000071, 392, 2134256023, 1644536696, '2025-03-17 09:11:33'),
       (34000072, 172, 1959626892, 1441829704, '2025-03-17 10:03:26'),
       (34000074, 187, 2077653805, 209228867, '2025-03-17 12:29:42'),
       (34000076, 187, 722305301, 1748179060, '2025-03-17 08:45:11'),
       (34000078, 404, 863065342, 1034318105, '2025-03-18 07:27:32'),
       (34000079, 403, 784610550, 639821772, '2025-03-17 13:29:25'),
       (34000082, 171, 892877884, 365868200, '2025-03-17 10:59:17'),
       (34000088, 392, 1990980066, 1968154734, '2025-03-17 09:28:11'),
       (34000089, 392, 1692635837, 911667970, '2025-03-17 19:29:57'),
       (34000090, 392, 1623130698, 1834108086, '2025-03-20 14:36:57'),
       (34000091, 390, 135106141, 1814561204, '2025-03-17 07:01:31'),
       (34000092, 404, 255710549, 1669979807, '2025-03-18 07:27:32'),
       (34000094, 187, 1958209334, 1074979983, '2025-03-20 14:36:56'),
       (34000095, 241, 1047484858, 204802253, '2025-03-18 10:27:55'),
       (34000097, 392, 1194847871, 266794196, '2025-03-18 10:37:09'),
       (34000104, 390, 1383157163, 309105451, '2025-03-17 21:34:33'),
       (34000106, 392, 275733264, 121114050, '2025-03-18 09:20:51'),
       (34000107, 404, 1212082003, 1228737365, '2025-03-18 07:27:31'),
       (34000108, 392, 635128334, 695573021, '2025-03-18 09:48:40'),
       (34000109, 389, 1209454070, 256390405, '2025-03-17 10:03:25'),
       (34000114, 172, 1135878723, 1654381049, '2025-03-17 21:04:11'),
       (34000122, 392, 2053275393, 1619133958, '2025-03-18 07:27:31'),
       (34000123, 392, 701960391, 1680198079, '2025-03-17 15:34:13'),
       (34000127, 392, 733840190, 1971892584, '2025-03-17 11:26:18'),
       (34000133, 123, 500498682, 815355423, '2025-03-18 07:27:30'),
       (34000134, 392, 1798775336, 2107587663, '2025-03-17 15:29:53'),
       (34000138, 392, 383277715, 262996030, '2025-03-17 19:03:32'),
       (34000139, 152, 991954957, 2053909243, '2025-03-18 07:27:30'),
       (34000140, 171, 1707808342, 988949789, '2025-03-17 09:40:27'),
       (34000142, 117, 1307374604, 537347556, '2025-03-17 12:03:46'),
       (34000143, 392, 278035659, 979882641, '2025-03-17 17:30:26'),
       (34000145, 187, 1735736819, 119078204, '2025-03-18 07:27:29'),
       (34000146, 142, 122344667, 1644822223, '2025-03-18 07:27:29'),
       (34000153, 389, 994133251, 1552858854, '2025-03-18 07:57:36'),
       (34000154, 389, 1676964561, 1173298973, '2025-03-18 05:39:58'),
       (34000155, 404, 464882185, 306124275, '2025-03-17 09:40:26'),
       (34000157, 392, 451933251, 1639984563, '2025-03-17 13:34:22'),
       (34000158, 390, 14039636, 2060576424, '2025-03-17 13:29:24'),
       (34000159, 125, 1278148659, 1086243462, '2025-03-18 07:27:28'),
       (34000161, 404, 883297833, 2005149664, '2025-03-20 15:45:48'),
       (34000164, 403, 890507475, 2041128791, '2025-03-17 20:29:45'),
       (34000173, 392, 1997105967, 1107135808, '2025-03-20 14:36:56'),
       (34000174, 125, 876652948, 1118132770, '2025-03-18 07:27:28'),
       (34000181, 125, 127246369, 1262476216, '2025-03-17 15:29:52'),
       (34000183, 389, 1390919007, 1640267682, '2025-03-17 10:03:25'),
       (34000184, 390, 814439712, 1293561588, '2025-03-17 21:34:33'),
       (34000189, 172, 1386680061, 416395041, '2025-03-17 20:25:24'),
       (34000192, 390, 65904862, 534611500, '2025-03-20 14:36:40'),
       (34000193, 392, 672357769, 1841627636, '2025-03-18 07:27:27'),
       (34000196, 187, 732634230, 60093017, '2025-03-17 22:25:48'),
       (34000202, 392, 836539270, 1300886251, '2025-03-18 07:27:27'),
       (34000207, 400, 467514078, 722641774, '2025-03-17 12:03:45'),
       (34000210, 187, 1082605134, 11626869, '2025-03-18 07:27:26'),
       (34000221, 125, 1582479948, 1983015801, '2025-03-17 10:03:24'),
       (34000226, 392, 290681587, 1248543804, '2025-03-17 12:59:09'),
       (34000227, 392, 188261166, 98834693, '2025-03-17 14:02:40'),
       (34000229, 392, 404908954, 69662454, '2025-03-18 07:57:36'),
       (34000231, 125, 1065524273, 50835780, '2025-03-18 11:51:42'),
       (34000233, 392, 1286602861, 1687219532, '2025-03-20 14:36:39'),
       (34000234, 129, 662496612, 1624004014, '2025-03-18 07:27:26'),
       (34000239, 125, 418332176, 112610009, '2025-03-17 10:03:24'),
       (34000246, 400, 1013100752, 235793900, '2025-03-20 14:36:38'),
       (34000247, 125, 567471605, 1415774263, '2025-03-18 07:27:25');
