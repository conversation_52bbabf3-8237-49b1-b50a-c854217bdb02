name: Rust CI/CD

on:
  push:
  pull_request:
    branches:
      - master
  workflow_dispatch:
    inputs:
      tag:
        description: 'Docker image tag to push (e.g., v1.0.0, specific-feature)'
        required: true
        type: string

env:
  RUST_TOOLCHAIN: stable
  CARGO_TERM_COLOR: always
  IMAGE_NAME: ghcr.io/${{ github.repository }}
  CARGO_INCREMENTAL: 0

permissions:
  contents: read
  packages: write
  id-token: write

concurrency:
  cancel-in-progress: true
  group: ${{ github.workflow }}-${{ github.ref }}

jobs:
  test_deploy:
    name: Test and Deploy
    runs-on: ubuntu-24.04-arm

    steps:
      - name: Determine Docker Tag
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            DOCKER_TAG="${{ inputs.tag }}"
          elif [[ "${{ github.ref }}" == "refs/heads/master" ]]; then
            DOCKER_TAG="latest"
          else
            DOCKER_TAG="${{ github.sha }}"
          fi
          echo "DOCKER_TAG=$DOCKER_TAG" >> $GITHUB_ENV
          echo "Using Docker tag: $DOCKER_TAG"

      ###############################
      ##           Setup           ##
      ###############################
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: '0'

      - name: Setup go-task
        run: curl -sSf https://taskfile.dev/install.sh | sh -s -- -b $HOME/.local/bin

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Start API Dependency Docker Images (in Background)
        run: nohup task start-test-env-api-deps > pull.log 2>&1 &

      - name: Install Protocol Buffers compiler
        uses: awalsh128/cache-apt-pkgs-action@latest
        with:
          packages: protobuf-compiler libprotobuf-dev
          version: 1.0

      - name: Setup Rust toolchain
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: ${{ env.RUST_TOOLCHAIN }}
          override: true
          components: rustfmt, clippy

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: "build-cache"

      - name: Cargo Install cargo-nextest
        run: cargo install cargo-nextest

      ###############################
      ##           Build           ##
      ###############################
      - name: Check formatting
        run: cargo fmt --all -- --check

      - name: Run clippy
        run: cargo clippy --tests -- -D warnings

      - name: Build Docker image
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/arm64
          tags: ${{ env.IMAGE_NAME }}:${{ env.DOCKER_TAG }}
          load: true
          push: false
          cache-from: type=gha
          cache-to: type=gha,mode=max

      ###############################
      ##         Test Code         ##
      ###############################
      - name: Test Docker image
        run: NO_BUILD=1 IMAGE=${{ env.IMAGE_NAME }}:${{ env.DOCKER_TAG }} task test

      ###############################
      ##        Deploy Code        ##
      ###############################
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        if: github.event_name == 'workflow_dispatch' || github.ref == 'refs/heads/master'
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Push Docker image
        if: github.event_name == 'workflow_dispatch' || github.ref == 'refs/heads/master'
        run: docker push ${{ env.IMAGE_NAME }}:${{ env.DOCKER_TAG }}
